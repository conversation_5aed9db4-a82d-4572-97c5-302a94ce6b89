import { Switch, Spin } from 'antd';
import { useEffect, useState, useTransition } from 'react';
import { toast } from 'sonner';
import {
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
  type NotificationSettings as NotificationSettingsType,
} from '../../../api/userApi';
import styles from './NotificationSettings.module.scss';

const NotificationSettings = () => {
  const {
    data: serverSettings,
    isLoading: isLoadingSettings,
    error: loadError,
  } = useGetNotificationSettingsQuery();

  const [updateSettings] = useUpdateNotificationSettingsMutation();
  const [isPending, startTransition] = useTransition();

  const [localSettings, setLocalSettings] = useState<NotificationSettingsType>({
    emailMarketing: false,
    bonusNotifications: true,
    winNotifications: false,
    depositNotifications: false,
    withdrawalNotifications: false,
  });

  const [pendingUpdates, setPendingUpdates] = useState<Set<keyof NotificationSettingsType>>(
    new Set(),
  );

  // Sync local state with server data when it loads
  useEffect(() => {
    if (serverSettings) {
      setLocalSettings(serverSettings);
    }
  }, [serverSettings]);

  // Show error toast if loading fails
  useEffect(() => {
    if (loadError) {
      const errorMessage =
        (loadError as { data?: { message?: string }; message?: string })?.data?.message ||
        (loadError as { message?: string })?.message ||
        'Failed to load notification settings';
      toast.error(errorMessage);
    }
  }, [loadError]);

  const handleSettingChange = (key: keyof NotificationSettingsType, value: boolean) => {
    // Mark this setting as pending
    setPendingUpdates((prev) => new Set(prev).add(key));

    // Create updated settings object with current state
    const updatedSettings = {
      ...localSettings,
      [key]: value,
    };

    // Use transition for the optimistic update
    startTransition(() => {
      // Optimistically update local state
      setLocalSettings(updatedSettings);
    });

    // Perform the async operation
    const performUpdate = async () => {
      try {
        await updateSettings(updatedSettings).unwrap();
        toast.success('Notification setting updated successfully');
      } catch (error: unknown) {
        // Revert the optimistic update on error
        setLocalSettings((prev) => ({
          ...prev,
          [key]: !value,
        }));

        const errorMessage =
          (error as { data?: { message?: string }; message?: string })?.data?.message ||
          (error as { message?: string })?.message ||
          'Failed to update notification setting';
        toast.error(errorMessage);
        console.error('Update notification setting error:', error);
      } finally {
        // Remove this setting from the pending set
        setPendingUpdates((prev) => {
          const newSet = new Set(prev);
          newSet.delete(key);
          return newSet;
        });
      }
    };

    performUpdate();
  };

  const notificationOptions = [
    {
      key: 'emailMarketing' as const,
      title: 'Email marketing',
      description: 'Get updates on promotions and news directly to your email.',
    },
    {
      key: 'bonusNotifications' as const,
      title: 'Bonus notifications',
      description: 'Receive alerts when bonuses are available or about to expire.',
    },
    {
      key: 'winNotifications' as const,
      title: 'Win notifications',
      description: 'Celebrate your victories with instant updates on your wins.',
    },
    {
      key: 'depositNotifications' as const,
      title: 'Deposit notifications',
      description: 'Receive confirmation when your deposit is successful.',
    },
    {
      key: 'withdrawalNotifications' as const,
      title: 'Withdrawal notifications',
      description: 'Get updates when your withdrawal is processed.',
    },
  ];

  if (isLoadingSettings) {
    return (
      <div className={styles.notificationSettings}>
        <div className={styles.innerWrapper}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <p className={styles.loadingText}>Loading notification settings...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.notificationSettings}>
      <div className={styles.innerWrapper}>
        <div className={styles.header}>
          <h2 className={styles.title}>Notification Settings</h2>
        </div>

        <div className={styles.settingsList}>
          {notificationOptions.map((option) => (
            <div key={option.key} className={styles.settingItem}>
              <div className={styles.settingContent}>
                <h3 className={styles.settingTitle}>{option.title}</h3>
                <p className={styles.settingDescription}>{option.description}</p>
              </div>
              <div className={styles.switchWrapper}>
                <Switch
                  checked={localSettings[option.key]}
                  onChange={(checked) => handleSettingChange(option.key, checked)}
                  className={styles.switch}
                  disabled={isLoadingSettings || pendingUpdates.has(option.key)}
                  loading={pendingUpdates.has(option.key)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export { NotificationSettings };
